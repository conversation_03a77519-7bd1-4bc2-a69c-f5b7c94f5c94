<?php

namespace Modules\Rajapicker\Filament\admin\Resources;

use Modules\Rajapicker\Filament\admin\Resources\MediaResource\Pages;
use Modules\Rajapicker\Models\Media;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

use Modules\Rajapicker\Services\RajaPickerConfigService;
use Modules\Rajapicker\Services\RajaPickerThumbnailService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;


class MediaResource extends Resource
{
    protected static ?string $model = Media::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Media Library';

    protected static ?string $modelLabel = 'Media';

    protected static ?string $pluralModelLabel = 'Media';
    // protected static ?string $slug = 'cms/media';
    // protected static bool $shouldRegisterNavigation = true;
 protected static ?string $title = 'MEDIA ADMIN';
 


    protected static ?string $navigationGroup = 'Cms';

    protected static ?int $navigationSort = -5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->schema([
                        Section::make('Informasi Media')
                            ->schema([
                                TextInput::make('name')
                                    ->label('Nama File') ,
                      FileUpload::make('file_name'),
                            ]),

                        Section::make('Pengaturan')
                            ->schema([
                                Select::make('collection_name')
                                    ->label('Koleksi')
                                    ->options([
                                        'default' => 'Default',
                                        'gallery' => 'Gallery',
                                        'documents' => 'Dokumen',
                                        'avatars' => 'Avatar',
                                        'banners' => 'Banner',
                                        'products' => 'Produk',
                                        'cms' => 'CMS',
                                    ])
                                    ->required(),
                            ]),
                    ])
                    ->columns(2),
            ]);
            
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(false)
            ->defaultPaginationPageOption(20)
            ->paginationPageOptions([5, 10, 20, 25, 50, 'all'])
            ->columns([
                Stack::make([
                    // Header dengan preview dan nama file
                    Split::make([
                        ImageColumn::make('preview')
                            ->label('')
                            ->size(function () {
                                $configService = app(RajaPickerConfigService::class);
                                $defaults = $configService->getDefaults();
                                return $defaults['ui']['preview_size'] ?? 150;
                            })
                            ->square()
                            ->getStateUsing(function ($record) {
                                if (!$record->isImage()) {
                                    return null;
                                }

                                $configService = app(RajaPickerConfigService::class);

                                // Buat path file asli sesuai dengan struktur storage
                                $imagePath = 'uploads/' . $record->collection_name . '/' . $record->file_name;
                                $defaultPreviewSize = $configService->getThumbnailConfig()['default_preview_size'] ?? 'th';

                                // Generate URL thumbnail dengan domain lengkap
                                $thumbnailUrl = $configService->generateThumbnailUrl($imagePath, $defaultPreviewSize);

                                // Pastikan URL menggunakan domain lengkap dari APP_URL
                                if (!str_starts_with($thumbnailUrl, 'http')) {
                                    $thumbnailUrl = config('app.url') . $thumbnailUrl;
                                }

                                // Cek apakah thumbnail benar-benar ada, jika tidak fallback ke URL asli
                                $thumbnailService = app(RajaPickerThumbnailService::class);
                                if ($thumbnailService->thumbnailExists($imagePath, $defaultPreviewSize)) {
                                    return $thumbnailUrl;
                                }

                                // Fallback ke URL asli jika thumbnail tidak ada
                                try {
                                    return $record->getUrl();
                                } catch (\Exception) {
                                    // Fallback ke accessor custom jika ada error
                                    return $record->url;
                                }
                            })
                            ->tooltip(function ($record) {
                                $tooltipInfo = [];

                                // Nama file
                                $tooltipInfo[] = "📁 Nama File: " . $record->file_name;

                                // URL file
                                $tooltipInfo[] = "🔗 URL: " . $record->getUrl();

                                // Ukuran file
                                $sizeInKB = number_format($record->size / 1024, 1);
                                $tooltipInfo[] = "📊 Ukuran: " . $sizeInKB . " KB";

                                // Dimensi untuk gambar
                                if ($record->isImage()) {
                                    $customProperties = $record->getCustomProperty('dimensions');
                                    if ($customProperties && isset($customProperties['width']) && isset($customProperties['height'])) {
                                        $tooltipInfo[] = "📐 Dimensi: " . $customProperties['width'] . " × " . $customProperties['height'] . " px";
                                    } else {
                                        // Fallback: coba baca dimensi dari file asli
                                        try {
                                            $filePath = storage_path('app/public/uploads/' . $record->collection_name . '/' . $record->file_name);
                                            if (file_exists($filePath)) {
                                                $imageSize = getimagesize($filePath);
                                                if ($imageSize) {
                                                    $tooltipInfo[] = "📐 Dimensi: " . $imageSize[0] . " × " . $imageSize[1] . " px";
                                                }
                                            }
                                        } catch (\Exception) {
                                            // Jika gagal membaca dimensi, skip
                                        }
                                    }
                                }

                                // MIME type
                                $tooltipInfo[] = "🏷️ Tipe: " . $record->mime_type;

                                // Collection
                                $tooltipInfo[] = "📂 Collection: " . ucfirst($record->collection_name);

                                return implode("\n", $tooltipInfo);
                            })
                            ->defaultImageUrl(url('/noimage.jpg'))
                            ->extraAttributes(['class' => 'rounded-lg'])
                            ->grow(false),

                        Stack::make([])->space(1),
                    ])->from('md'),

                    Split::make([
                        Stack::make([
                            TextColumn::make('name')
                                ->label('')
                                // ->weight(FontWeight::Bold)
                                ->searchable()
                                ->sortable()
                                ->limit(20)
                                ->tooltip(function (TextColumn $column): ?string {
                                    $state = $column->getState();
                                    return strlen($state) > 20 ? $state : null;
                                }),

                            // Tampilkan full URL thumbnail untuk pemeriksaan
                            // TextColumn::make('thumbnail_url')
                            //     ->label('')
                            //     ->getStateUsing(function ($record) {
                            //         if (!$record->isImage()) {
                            //             return 'Bukan gambar';
                            //         }

                            //         $configService = app(RajaPickerConfigService::class);

                            //         // Buat path file asli sesuai dengan struktur storage
                            //         $imagePath = 'uploads/' . $record->collection_name . '/' . $record->file_name;
                            //         $defaultPreviewSize = $configService->getThumbnailConfig()['default_preview_size'] ?? 'th';

                            //         // Generate URL thumbnail dengan domain lengkap
                            //         $thumbnailUrl = $configService->generateThumbnailUrl($imagePath, $defaultPreviewSize);

                            //         // Pastikan URL menggunakan domain lengkap dari APP_URL
                            //         if (!str_starts_with($thumbnailUrl, 'http')) {
                            //             $thumbnailUrl = config('app.url') . $thumbnailUrl;
                            //         }

                            //         return $thumbnailUrl;
                            //     })
                            //     ->color('gray')
                            //     ->wrap(true)
                            //     ->size('xs')
                            //     ->limit(60)
                            //     ->tooltip(function (TextColumn $column): ?string {
                            //         $state = $column->getState();
                            //         return strlen($state) > 60 ? $state : null;
                            //     })
                            //     ->copyable()
                            //     ->copyMessage('URL thumbnail disalin!')
                            //     ->copyMessageDuration(1500),
                        ])->space(1),

                    ]),
           

                    // Info badges dan metadata
                    Split::make([
                        TextColumn::make('mime_type')
                            ->label('')
                            ->badge()
                            ->color(function ($state) {
                                if (str_starts_with($state, 'image/')) return 'success';
                                if (str_starts_with($state, 'video/')) return 'info';
                                if (str_starts_with($state, 'audio/')) return 'warning';
                                if (str_starts_with($state, 'application/')) return 'secondary';
                                return 'gray';
                            })
                            ->formatStateUsing(function ($state) {
                                if (str_starts_with($state, 'image/')) return 'Gambar';
                                if (str_starts_with($state, 'video/')) return 'Video';
                                if (str_starts_with($state, 'audio/')) return 'Audio';
                                if (str_starts_with($state, 'application/')) return 'Dokumen';
                                return 'Lainnya';
                            })
                            ->grow(false),

                        TextColumn::make('collection_name')
                            ->label('')
                            ->badge()
                            ->color('primary')
                            ->sortable()
                            ->grow(false),

                      
                    ])->from('sm'),

                  Split::make([
                      TextColumn::make('id')
                        ->label('ID')
                        ->badge()
                        ->color('gray')
                        ->size('sm')
                        ->grow(false),
                          TextColumn::make('size')
                            ->label('')
                            ->formatStateUsing(fn($state): string => number_format($state / 1024, 1) . ' KB')
                            ->sortable()
                            ->color('gray')
                            ->size('sm')
                            ->grow(false),
                  ]),
                    // Tanggal dibuat
                    // TextColumn::make('created_at')
                    //     ->label('')
                    //     ->dateTime('d M Y H:i')
                    //     ->sortable()
                    //     ->color('gray')
                    //     ->size('sm'),

                ])->space(2),
            ])
            ->contentGrid([
                'sm' => 1,
                'md' => 2,
                'lg' => 4,
                'xl' => 5,
                '2xl' => 6,
            ])
            ->filters([
                SelectFilter::make('collection_name')
                    ->label('Koleksi')
                    ->options([
                        'default' => 'Default',
                        'gallery' => 'Gallery',
                        'documents' => 'Dokumen',
                        'avatars' => 'Avatar',
                        'banners' => 'Banner',
                        'products' => 'Produk',
                        'cms' => 'CMS',
                    ]),

                SelectFilter::make('mime_type')
                    ->label('Tipe File')
                    ->options([
                        'image' => 'Gambar',
                        'video' => 'Video',
                        'audio' => 'Audio',
                        'application' => 'Dokumen',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query, $value): Builder => $query->where('mime_type', 'like', $value . '/%'),
                        );
                    }),

                Tables\Filters\Filter::make('images_only')
                    ->label('Hanya Gambar')
                    ->query(fn(Builder $query): Builder => $query->where('mime_type', 'like', 'image/%'))
                    ->toggle(),
            ])
            ->actions([
                // Action::make('view')
                //     ->label('Lihat')
                //     ->icon('heroicon-o-eye')
                //     ->url(fn(Media $record): string => $record->url ?? '#')
                //     ->openUrlInNewTab()
                //     ->visible(fn(Media $record): bool => $record->isImage()),

                Action::make('edit_image')
                    ->label(false)
                    ->icon('heroicon-o-pencil')
                    ->color('primary')
                    ->url(fn(Media $record): string => \Modules\Rajapicker\Filament\admin\Pages\RajaImageEditorPage::getUrl(['media' => $record->id]))
                    ->visible(fn(Media $record): bool => $record->isImage()),

                Action::make('download')
                    ->label(false)
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(fn(Media $record): string => $record->url ?? '#')
                    ->openUrlInNewTab(),

                // Tables\Actions\EditAction::make()
                //     ->label('Edit'),

                Tables\Actions\DeleteAction::make()
                    ->label(false)
                    ->before(function (Media $record) {
                        static::deleteThumbnails($record);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->before(function ($records) {
                            foreach ($records as $record) {
                                static::deleteThumbnails($record);
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMedia::route('/'),
            'create' => Pages\CreateMedia::route('/create'),
            'edit' => Pages\EditMedia::route('/{record}/edit'),
            'view' => Pages\ViewMedia::route('/{record}'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return number_format(static::getModel()::count());
    }

    /**
     * Hapus semua thumbnail yang terkait dengan media
     */
    protected static function deleteThumbnails(Media $media): void
    {
        try {
            // Hanya proses jika media adalah gambar
            if (!$media->isImage()) {
                return;
            }

            Log::info('Menghapus thumbnail untuk media ID: ' . $media->id . ' (' . $media->file_name . ')');

            // Ambil konfigurasi thumbnail dari RajaPicker
            $thumbnailConfig = config('rajapicker.storage.thumbnail', []);

            if (!$thumbnailConfig['enabled'] ?? false) {
                Log::info('Thumbnail generation tidak aktif, skip penghapusan thumbnail');
                return;
            }

            // Path file asli
            $originalPath = 'uploads/' . $media->collection_name . '/' . $media->file_name;
            $thumbnailDirectory = $thumbnailConfig['directory'] ?? 'thumbnails';

            // Ambil semua ukuran thumbnail yang dikonfigurasi
            $thumbnailSizes = $thumbnailConfig['sizes'] ?? [];
            $deletedCount = 0;

            foreach ($thumbnailSizes as $sizeName => $sizeConfig) {
                // Skip jika ukuran tidak aktif
                if (!($sizeConfig['enabled'] ?? true)) {
                    continue;
                }

                $suffix = $sizeConfig['suffix'] ?? $sizeName;

                // Generate nama file thumbnail
                $pathInfo = pathinfo($originalPath);
                $thumbnailBaseName = $pathInfo['filename'] . '_' . $suffix;

                // Cek untuk format WebP (prioritas utama)
                $webpThumbnailPath = $pathInfo['dirname'] . '/' . $thumbnailDirectory . '/' . $thumbnailBaseName . '.webp';

                // Cek untuk format asli
                $originalFormatThumbnailPath = $pathInfo['dirname'] . '/' . $thumbnailDirectory . '/' . $thumbnailBaseName . '.' . $pathInfo['extension'];

                // Hapus thumbnail WebP jika ada
                if (Storage::disk('public')->exists($webpThumbnailPath)) {
                    Storage::disk('public')->delete($webpThumbnailPath);
                    Log::info('Thumbnail WebP dihapus: ' . $webpThumbnailPath);
                    $deletedCount++;
                }

                // Hapus thumbnail format asli jika ada
                if (Storage::disk('public')->exists($originalFormatThumbnailPath)) {
                    Storage::disk('public')->delete($originalFormatThumbnailPath);
                    Log::info('Thumbnail format asli dihapus: ' . $originalFormatThumbnailPath);
                    $deletedCount++;
                }
            }

            Log::info('Total ' . $deletedCount . ' thumbnail berhasil dihapus untuk media ID: ' . $media->id);

        } catch (\Exception $e) {
            Log::error('Error saat menghapus thumbnail untuk media ID: ' . $media->id . ' - ' . $e->getMessage());
        }
    }
}
