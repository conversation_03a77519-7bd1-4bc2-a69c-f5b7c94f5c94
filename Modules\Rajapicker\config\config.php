<?php

return [
    'name' => 'Rajapicker',
    
    /*
    |--------------------------------------------------------------------------
    | RajaPicker Configuration
    |--------------------------------------------------------------------------
    |
    | Konfigurasi default untuk komponen RajaPicker field
    |
    */
    
    
    'defaults' => [
        // Tipe file yang diterima (MIME types)
        'accepted_file_types' => [
            'image/jpeg',
            'image/png', 
            'image/gif',
            'image/webp'
        ],
        
        // Ukuran maksimal file dalam MB
        'max_file_size' => 10,
        
        // Collection default untuk media
        'collection' => 'default',
        
        // Mode multiple selection
        'multiple' => false,
        
        // Direktori upload custom (opsional)
        'directory' => null,
        
        // Fitur yang diaktifkan
        'features' => [
            'picker' => true,      // Aktifkan fitur picker
            'uploader' => true,    // Aktifkan fitur uploader
        ],
        
        // UI Settings
        'ui' => [
            'placeholder' => 'Pilih atau upload gambar...',
            'preview_size' => 150, // ukuran preview dalam pixel
            'show_file_name' => true,
            'show_file_size' => true,
        ],
        
        // Pagination untuk picker
        'per_page' => 12,
        
        // Filter berdasarkan user
        'user_filter' => [
            'by_user' => false,        // Filter berdasarkan user yang sedang login
            'by_user_id' => null,      // Filter berdasarkan user ID tertentu
        ],
        
 
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Collections Configuration
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk berbagai collection yang dapat digunakan
    |
    */
    
    'collections' => [
        'default' => [
            'name' => 'Default ',
            'description' => 'Collection default untuk media umum',
            'accepted_file_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'max_file_size' => 10,
            'directory' => null,
        ],
        
        'produk' => [
            'name' => 'foto produk',
            'description' => 'Collection untuk gambar produk',
            'accepted_file_types' => ['image/jpeg', 'image/png', 'image/webp'],
            'max_file_size' => 5,
            'directory' => 'produk',
        ],
        
        'lainlain' => [
            'name' => 'lain lain',
            'description' => 'Collection untuk gambar banner',
            'accepted_file_types' => ['image/jpeg', 'image/png', 'image/webp'],
            'max_file_size' => 15,
            'directory' => 'lainlain',
        ],
        
        'gallery' => [
            'name' => 'Gallery ',
            'description' => 'Collection untuk galeri gambar',
            'accepted_file_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'max_file_size' => 8,
            'directory' => 'gallery',
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Aturan validasi default untuk upload file
    |
    */
    
    'validation' => [
        'rules' => [
            'file' => 'required|file|mimes:jpeg,jpg,png,gif,webp|max:2240', // 10MB max
        ],
        
        'messages' => [
            'file.required' => 'File gambar harus dipilih.',
            'file.file' => 'File yang diupload harus berupa file yang valid.',
            'file.mimes' => 'File harus berupa gambar dengan format: jpeg, jpg, png, gif, atau webp.',
            'file.max' => 'Ukuran file tidak boleh lebih dari 10MB.',
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Storage Configuration
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk penyimpanan file
    |
    */
    
    'storage' => [
        'disk' => 'public',
        'path' => 'rajapicker',
        'url_prefix' => '/storage',
        
        
        // Konfigurasi untuk WebP conversion
        'webp' => [
            'enabled' => true,
            'quality' => 85,
            'default' => true,
            'preserve_original' => true,
        ],
        
        // Konfigurasi untuk thumbnail
        'thumbnail' => [
            'enabled' => true,
            'directory' => 'thumbnails',
            'prefix' => '{name}',
            'webp_conversion' => true,
            'sizes' => [
                '25persen' => [
                    'percentage' => 25,
                    'quality' => 80,
                    'suffix' => '25p',
                    'enabled' => false,
                ],
                '50persen' => [
                    'percentage' => 50,
                    'quality' => 85,
                    'suffix' => '50p',
                    'enabled' => true,
                ],
                '75persen' => [
                    'percentage' => 75,
                    'quality' => 90,
                    'suffix' => '75p',
                    'enabled' => false,
                ],
                'th' => [
                    'width' => 150,
                    'height' => 150,
                    'quality' => 80,
                    'suffix' => 'th',
                    'enabled' => true,
                ],
            ],
            // ukuran thumbnail default untuk preview RajaPicker
            'default_preview_size' => 'th',
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | UI Themes
    |--------------------------------------------------------------------------
    |
    | Konfigurasi tema untuk interface RajaPicker
    |
    */
    
    'themes' => [
        'default' => [
            'preview_size' => 150,
            'grid_columns' => 4,
            'show_file_info' => true,
            'show_upload_progress' => true,
        ],
        
        'compact' => [
            'preview_size' => 100,
            'grid_columns' => 6,
            'show_file_info' => false,
            'show_upload_progress' => true,
        ],
        
        'detailed' => [
            'preview_size' => 200,
            'grid_columns' => 3,
            'show_file_info' => true,
            'show_upload_progress' => true,
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk optimasi performa
    |
    */
    
    'performance' => [
        'cache_media_list' => true,
        'cache_ttl' => 300, // 5 menit
        'lazy_loading' => true,
        'max_media_per_page' => 200,
        'thumbnail_generation' => [
            'enabled' => true,
            'sizes' => [
                'thumb' => [150, 150],
                'medium' => [300, 300],
                'large' => [600, 600],
            ],
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan keamanan untuk upload dan akses file
    |
    */
    
    'security' => [
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'max_file_size_bytes' => 10485760, // 10MB dalam bytes
        'scan_for_viruses' => false, // Perlu ekstensi antivirus
        'validate_mime_type' => true,
        'prevent_duplicate_uploads' => true,
    ],
    
    // Pengaturan penamaan file media yang di-upload
    'file_naming' => [
        // selalu lowercase nama file
        'lowercase' => true,

        // karakter pemisah antar bagian nama
        'separator' => '_',

        // tambahkan timestamp (Unix time) di nama file
        'append_timestamp' => false,

        // panjang string random yang ditambahkan di akhir (0 untuk menonaktifkan)
        'append_random_length' => 8,

        /*
         |------------------------------------------------------------------
         | Pola format nama
         |------------------------------------------------------------------
         | Placeholder yang tersedia:
         | {name}       => nama asli (setelah di-slug)
         | {timestamp}  => waktu upload (unix time)
         | {random}     => string random sesuai panjang di atas
         | {sep}        => karakter separator
         */
        'pattern' => '{name}{sep}{timestamp}{sep}{random}',
    ],
];
