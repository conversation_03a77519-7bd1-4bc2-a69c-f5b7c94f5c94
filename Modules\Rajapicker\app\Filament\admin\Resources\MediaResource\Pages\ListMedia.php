<?php

namespace Modules\Rajapicker\Filament\admin\Resources\MediaResource\Pages;

use Modules\Rajapicker\Models\RajaGaleri;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Modules\Rajapicker\Filament\admin\Resources\MediaResource;
use Modules\Rajapicker\Models\Media;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Intervention\Image\ImageManager;
// use Intervention\Image\Drivers\Gd\Driver as GdDriver; // tidak terpakai
// use Intervention\Image\Encoders\WebpEncoder; // tidak terpakai

class ListMedia extends ListRecords
{
    protected static string $resource = MediaResource::class;

    /**
     * Membuat FileUpload yang mengikuti konfigurasi RajaPicker
     */
    protected function createConfigurableFileUpload(): FileUpload
    {
        // Ambil konfigurasi dari config RajaPicker
        $config = config('rajapicker.defaults', []);
        $storageConfig = config('rajapicker.storage', []);
        $uiConfig = $config['ui'] ?? [];
        $thumbnailConfig = $config['thumbnail'] ?? [];
        $themeConfig = config('rajapicker.themes.default', []);
        $fileNamingConfig = config('rajapicker.file_naming', []);

        // Gabungkan accepted file types dari config dengan file types tambahan untuk media library
        $acceptedTypes = array_merge(
            $config['accepted_file_types'] ?? ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            [
                'image/svg+xml',
                'application/pdf',
                'video/mp4',
                'video/avi',
                'video/mov',
                'audio/mp3',
                'audio/wav',
            ]
        );

        // Ambil max file size dari config (dalam MB)
        $maxFileSize = $config['max_file_size'] ?? 10;
        $maxFileSizeKB = $maxFileSize * 1024; // Convert ke KB untuk Filament

        // Ambil directory dari storage config
        $directory = $storageConfig['path'] ?? 'media-uploads';

        // Ambil disk dari storage config
        $disk = $storageConfig['disk'] ?? 'public';

        // Ambil preview size dari UI config atau theme config
        $previewSize = $uiConfig['preview_size'] ?? $themeConfig['preview_size'] ?? 150;

        // Ambil thumbnail settings
        $thumbnailEnabled = $thumbnailConfig['enabled'] ?? true;
        $thumbnailSizes = $thumbnailConfig['sizes'] ?? [];
        $defaultThumbnailSize = $thumbnailConfig['default_preview_size'] ?? 'th';

        // Cari ukuran thumbnail default
        $thumbnailWidth = null;
        $thumbnailHeight = null;
        if ($thumbnailEnabled && isset($thumbnailSizes[$defaultThumbnailSize])) {
            $thumbnailWidth = $thumbnailSizes[$defaultThumbnailSize]['width'] ?? null;
            $thumbnailHeight = $thumbnailSizes[$defaultThumbnailSize]['height'] ?? null;
        }

        // Buat FileUpload dengan konfigurasi lengkap
        $fileUpload = FileUpload::make('files')
            ->label('File')
            ->multiple()
            ->acceptedFileTypes($acceptedTypes)
            ->maxSize($maxFileSizeKB)
            ->maxFiles(10)
            ->previewable()
            ->reorderable()
            ->required()
            ->helperText("Maksimal 10 file, ukuran per file maksimal {$maxFileSize}MB. Tipe file yang didukung: " . implode(', ', array_map(fn($type) => str_replace(['image/', 'application/', 'video/', 'audio/'], '', $type), $acceptedTypes)))
            ->directory($directory)
            ->disk($disk);

        // Tambahkan konfigurasi preview untuk gambar
        $fileUpload->imagePreviewHeight($previewSize);

        // Tambahkan konfigurasi resize jika thumbnail diaktifkan dan ukuran tersedia
        if ($thumbnailEnabled && $thumbnailWidth && $thumbnailHeight) {
            $fileUpload
                ->imageResizeMode('cover')
                ->imageResizeTargetWidth($thumbnailWidth)
                ->imageResizeTargetHeight($thumbnailHeight);
        }

        // Tambahkan konfigurasi UI tambahan dari theme
        if (isset($themeConfig['show_upload_progress']) && $themeConfig['show_upload_progress']) {
            $fileUpload->uploadProgressIndicatorPosition('left');
        }

        // Tambahkan konfigurasi file naming dari RajaPicker
        if ($fileNamingConfig) {
            $fileUpload->getUploadedFileNameForStorageUsing(function ($file) use ($fileNamingConfig) {
                return $this->generateFileNameFromConfig($file, $fileNamingConfig);
            });
        }

        return $fileUpload;
    }

    /**
     * Generate nama file berdasarkan konfigurasi RajaPicker file_naming
     */
    protected function generateFileNameFromConfig($file, array $fileNamingConfig): string
    {
        $extension = strtolower($file->getClientOriginalExtension());

        // Nama dasar
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $separator = $fileNamingConfig['separator'] ?? '_';
        $nameSlug = \Illuminate\Support\Str::slug($originalName, $separator);

        if ($fileNamingConfig['lowercase'] ?? true) {
            $nameSlug = strtolower($nameSlug);
        }

        // Token timestamp dan random
        $timestamp = ($fileNamingConfig['append_timestamp'] ?? false) ? time() : '';
        $randomLen = (int) ($fileNamingConfig['append_random_length'] ?? 8);
        $random = $randomLen > 0 ? \Illuminate\Support\Str::lower(\Illuminate\Support\Str::random($randomLen)) : '';

        // Build pattern
        $pattern = $fileNamingConfig['pattern'] ?? '{name}{sep}{random}';

        $filenameWithoutExt = str_replace([
            '{name}',
            '{timestamp}',
            '{random}',
            '{sep}'
        ], [
            $nameSlug,
            $timestamp,
            $random,
            $separator
        ], $pattern);

        // Bersihkan kemungkinan separator ganda
        $filenameWithoutExt = preg_replace('/' . preg_quote($separator, '/') . '{2,}/', $separator, $filenameWithoutExt);
        $filenameWithoutExt = trim($filenameWithoutExt, $separator);

        return $filenameWithoutExt . '.' . $extension;
    }

    /**
     * Generate nama file custom untuk Spatie Media Library
     */
    protected function generateCustomFileName(string $originalFileName, array $fileNamingConfig): string
    {
        $extension = strtolower(pathinfo($originalFileName, PATHINFO_EXTENSION));

        // Nama dasar
        $originalName = pathinfo($originalFileName, PATHINFO_FILENAME);
        $separator = $fileNamingConfig['separator'] ?? '_';
        $nameSlug = \Illuminate\Support\Str::slug($originalName, $separator);

        if ($fileNamingConfig['lowercase'] ?? true) {
            $nameSlug = strtolower($nameSlug);
        }

        // Token timestamp dan random
        $timestamp = ($fileNamingConfig['append_timestamp'] ?? false) ? time() : '';
        $randomLen = (int) ($fileNamingConfig['append_random_length'] ?? 8);
        $random = $randomLen > 0 ? \Illuminate\Support\Str::lower(\Illuminate\Support\Str::random($randomLen)) : '';

        // Build pattern
        $pattern = $fileNamingConfig['pattern'] ?? '{name}{sep}{random}';

        $filenameWithoutExt = str_replace([
            '{name}',
            '{timestamp}',
            '{random}',
            '{sep}'
        ], [
            $nameSlug,
            $timestamp,
            $random,
            $separator
        ], $pattern);

        // Bersihkan kemungkinan separator ganda
        $filenameWithoutExt = preg_replace('/' . preg_quote($separator, '/') . '{2,}/', $separator, $filenameWithoutExt);
        $filenameWithoutExt = trim($filenameWithoutExt, $separator);

        return $filenameWithoutExt . '.' . $extension;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('upload')
                ->label('Upload Media')
                ->icon('heroicon-o-cloud-arrow-up')
                ->color('primary')
                ->form([
                    Grid::make()
                        ->schema([
                            Section::make('Upload File')
                                ->description('Pilih file yang ingin diupload ke media library')
                                ->schema([
                                    $this->createConfigurableFileUpload(),

                                    Select::make('collection_name')
                                        ->label('Koleksi')
                                        ->options($this->getAvailableCollections())
                                        ->default('default')
                                        ->required()
                                        ->helperText('Pilih koleksi untuk mengorganisir file'),
                                ]),
                        ])
                        ->columns(1),
                ])
                ->action(function (array $data) {
                    $this->handleSpatieMediaUpload($data);
                })
                ->modalWidth('2xl'),


            Actions\CreateAction::make()
                ->label('Tambah Media Manual')
                ->icon('heroicon-o-document-plus'),

            Action::make('cleanup')
                ->label('Bersihkan File Tidak Terpakai')
                ->icon('heroicon-o-trash')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Bersihkan File Tidak Terpakai')
                ->modalDescription('Apakah Anda yakin ingin menghapus file media yang tidak terhubung dengan model apapun?')
                ->action(function () {
                    $deletedCount = $this->cleanupUnusedMedia();
                    Notification::make()
                        ->title("Berhasil menghapus {$deletedCount} file yang tidak terpakai!")
                        ->success()
                        ->send();
                }),

            Action::make('generate_thumbnails')
                ->label('Generate Semua Thumbnail')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Generate Thumbnail')
                ->modalDescription('Generate thumbnail untuk semua gambar (gambar yang sudah memiliki thumbnail akan diabaikan). Lanjutkan?')
                ->action(function () {
                    $this->generateThumbnails();
                }),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('Semua Collection')
                ->badge(Media::where('mime_type', 'LIKE', 'image/%')->count()),
        ];

        // Dapatkan semua collection yang tersedia dari database
        $collections = Media::where('mime_type', 'LIKE', 'image/%')
            ->where('collection_name', '!=', 'conversion')
            ->whereNotLike('file_name', '%/conversion/%')
            ->distinct()
            ->pluck('collection_name')
            ->filter()
            ->sort();

        // Tambahkan tab untuk setiap collection
        foreach ($collections as $collection) {
            $count = Media::where('collection_name', $collection)
                ->where('mime_type', 'LIKE', 'image/%')
                ->count();

            $tabs[$collection] = Tab::make(ucfirst($collection))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('collection_name', $collection))
                ->badge($count);
        }

        return $tabs;
    }

    /**
     * Get available collections for upload form - menggunakan konfigurasi RajaPicker
     */
    protected function getAvailableCollections(): array
    {
        // Ambil collections dari konfigurasi RajaPicker
        $rajaPickerCollections = config('rajapicker.collections', []);
        $configCollections = [];

        foreach ($rajaPickerCollections as $key => $collection) {
            $configCollections[$key] = $collection['name'] ?? ucfirst($key);
        }

        // Dapatkan collection yang sudah ada di database
        $existingCollections = Media::distinct()
            ->pluck('collection_name')
            ->filter()
            ->mapWithKeys(function ($collection) {
                return [$collection => ucfirst($collection)];
            })
            ->toArray();

        // Gabungkan collections dari config dengan yang ada di database
        $allCollections = array_merge($configCollections, $existingCollections);
        ksort($allCollections);

        return $allCollections;
    }

    protected function cleanupUnusedMedia(): int
    {
        // Cari media yang tidak terhubung dengan model apapun (kecuali standalone)
        $unusedMedia = \Modules\Rajapicker\Models\Media::where(function ($query) {
            $query->whereDoesntHave('model')
                ->where('model_type', '!=', 'standalone');
        })->get();

        $count = $unusedMedia->count();

        foreach ($unusedMedia as $media) {
            $media->delete();
        }

        return $count;
    }

    public function getTitle(): string
    {
        return 'Media Library';
    }

    protected function handleSpatieMediaUpload(array $data)
    {
        try {
            $uploadedFiles = $data['files'] ?? [];
            $collectionName = $data['collection_name'] ?? 'default';
            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            // Ambil konfigurasi collection-specific dari RajaPicker
            $collectionConfig = config("rajapicker.collections.{$collectionName}", []);
            $fileNamingConfig = config('rajapicker.file_naming', []);

            // Debug dengan dd untuk melihat data langsung
            if (empty($uploadedFiles)) {
                Notification::make()
                    ->title('Tidak ada file yang dipilih!')
                    ->body('Data yang diterima: ' . json_encode($data))
                    ->danger()
                    ->send();
                return;
            }

    

            // Debug: Tampilkan data di notifikasi untuk debugging
            Notification::make()
                ->title('Debug: Data Upload')
                ->body('Files: ' . json_encode($uploadedFiles) . ' | Collection: ' . $collectionName)
                ->info()
                ->send();

            foreach ($uploadedFiles as $uploadedFile) {
                try {
                    // Debug: Log file yang sedang diproses
                    // Log::info('Processing file', ['file' => $uploadedFile]);

                    // Buat record RajaGaleri baru untuk setiap file
                    $mediaRecord = new RajaGaleri();
                    $mediaRecord->save(); 
                    // Simpan dulu untuk mendapatkan ID

                    // Coba menggunakan Storage facade untuk mendapatkan path yang benar
                    $tempPath = null;
                    $originalName = $uploadedFile;

                    $mlprefix = config('media-library.prefix', 'uploads');
                    // Coba berbagai disk dan path
                    $diskPaths = [
                        ['disk' => 'public', 'path' => $mlprefix . '/' . $uploadedFile],
                        ['disk' => 'public', 'path' => $uploadedFile],
                        ['disk' => 'local', 'path' => 'livewire-tmp/' . $uploadedFile],
                        ['disk' => 'local', 'path' => $uploadedFile],
                    ];

                    foreach ($diskPaths as $diskPath) {
                        if (Storage::disk($diskPath['disk'])->exists($diskPath['path'])) {
                            $tempPath = Storage::disk($diskPath['disk'])->path($diskPath['path']);
                      
                            break;
                        }
                    }
 
                    $originalName = basename($uploadedFile);
                    $fileName = pathinfo($uploadedFile, PATHINFO_FILENAME);

                    // Generate nama file menggunakan konfigurasi RajaPicker
                    $customFileName = $this->generateCustomFileName($originalName, $fileNamingConfig);

                    // Tambahkan file ke media collection dengan nama file yang sudah dikonfigurasi
                    $media = $mediaRecord
                        ->addMedia($tempPath)
                        ->usingName($fileName)
                        ->usingFileName($customFileName)
                        ->toMediaCollection($collectionName);

                    if ($media) {
                        $successCount++;

                        $mediaRecord->update([
                            'record_id' => $media->id,
                            'nama' => $media->name,
                            'key' => $media->uuid,
                            'value' =>   $media->getUrl(),
                        ]);

                        // Generate thumbnail otomatis setelah upload berhasil
                        Log::info('Generating thumbnails for media ID: ' . $media->id);
                        $this->generateThumbnailForMedia($media);
                    } else {
                        // $errors[] = "Gagal menyimpan: " . $originalName;
                        $errorCount++;
                    }

                } catch (\Exception $e) {
                    $errors[] = "Error pada " . basename($uploadedFile) . ": " . $e->getMessage();
                    $errorCount++;
                    // Log::error('Media upload error: ' . $e->getMessage(), [
                    //     'file' => $uploadedFile,
                    //     'collection' => $collectionName
                    // ]);
                }
            }

            // Kirim notifikasi berdasarkan hasil
            if ($successCount > 0 && $errorCount === 0) {
                Notification::make()
                    ->title("Berhasil mengupload {$successCount} file!")
                    ->success()
                    ->send();
            } elseif ($successCount > 0 && $errorCount > 0) {
                Notification::make()
                    ->title("Upload selesai dengan peringatan")
                    ->body("Berhasil: {$successCount} file, Gagal: {$errorCount} file")
                    ->warning()
                    ->send();
            } else {
                Notification::make()
                    ->title("Upload gagal!")
                    ->body("Semua file gagal diupload. " . implode(', ', array_slice($errors, 0, 3)))
                    ->danger()
                    ->send();
            }

        } catch (\Exception $e) {
            Log::error('Media upload general error: ' . $e->getMessage());

            Notification::make()
                ->title('Terjadi kesalahan saat upload!')
                ->body('Error: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \Modules\Rajapicker\Filament\admin\Resources\MediaResource\Widgets\MediaStatsWidget::class,
        ];
    }

    protected function generateThumbnails(): void
    {
        $prefix = config('media-library.prefix', 'uploads');
        $generated = 0;

        $manager = ImageManager::gd();

        Media::where('mime_type', 'like', 'image/%')->chunk(100, function ($medias) use (&$generated, $manager, $prefix) {
            foreach ($medias as $record) {
                $disk = $record->disk ?? 'public';

                $thumbName = pathinfo($record->file_name, PATHINFO_FILENAME) . '_thumb.webp';
                $relativeThumb = $record->collection_name . '/' . $thumbName;

                if (Storage::disk($disk)->exists($prefix . '/' . $relativeThumb)) {
                    continue; // sudah ada thumbnail
                }

                $originalRelative = $record->collection_name . '/' . $record->file_name;
                if (!Storage::disk($disk)->exists($prefix . '/' . $originalRelative)) {
                    continue; // file asli tidak ditemukan
                }

                $originalContent = Storage::disk($disk)->get($prefix . '/' . $originalRelative);

                try {
                    $encoded = $manager
                        ->read($originalContent)
                        ->scaleDown(300, 300)
                        ->toWebp(quality: 80);

                    Storage::disk($disk)->put($prefix . '/' . $relativeThumb, (string) $encoded);
                    $generated++;
                } catch (\Throwable $e) {
                    Log::error('Gagal generate thumbnail: ' . $e->getMessage());
                    continue;
                }
            }
        });

        Notification::make()
            ->title("Selesai! {$generated} thumbnail berhasil dibuat.")
            ->success()
            ->send();
    }

    /**
     * Generate thumbnail untuk media yang baru diupload
     */
    protected function generateThumbnailForMedia($media): void
    {
        try {
            Log::info('Starting thumbnail generation for media: ' . $media->id);

            // Ambil konfigurasi thumbnail dari RajaPicker (path yang benar)
            $thumbnailConfig = config('rajapicker.storage.thumbnail', []);

            Log::info('Thumbnail config: ' . json_encode($thumbnailConfig));

            if (!($thumbnailConfig['enabled'] ?? true)) {
                Log::info('Thumbnail generation disabled');
                return; // Thumbnail generation disabled
            }

            $manager = ImageManager::gd();
            $disk = $media->disk ?? 'public';
            $prefix = config('media-library.prefix', 'uploads');

            // Ambil konfigurasi thumbnail sizes
            $thumbnailSizes = $thumbnailConfig['sizes'] ?? [];
            $webpConversion = $thumbnailConfig['webp_conversion'] ?? true;
            $thumbnailDirectory = $thumbnailConfig['directory'] ?? 'thumbnails';

            // Path file asli
            $originalRelative = $media->collection_name . '/' . $media->file_name;

            if (!Storage::disk($disk)->exists($prefix . '/' . $originalRelative)) {
                Log::warning('File asli tidak ditemukan untuk thumbnail generation: ' . $originalRelative);
                return;
            }

            $originalContent = Storage::disk($disk)->get($prefix . '/' . $originalRelative);
            $image = $manager->read($originalContent);

            // Generate thumbnail untuk setiap ukuran yang dikonfigurasi
            foreach ($thumbnailSizes as $sizeName => $sizeConfig) {
                if (!($sizeConfig['enabled'] ?? true)) {
                    continue;
                }

                // Tentukan ukuran berdasarkan konfigurasi
                if (isset($sizeConfig['percentage'])) {
                    // Gunakan percentage dari ukuran asli
                    $percentage = $sizeConfig['percentage'];
                    $originalWidth = $image->width();
                    $originalHeight = $image->height();
                    $width = (int) ($originalWidth * $percentage / 100);
                    $height = (int) ($originalHeight * $percentage / 100);
                } else {
                    // Gunakan width/height absolut
                    $width = $sizeConfig['width'] ?? 150;
                    $height = $sizeConfig['height'] ?? 150;
                }

                $quality = $sizeConfig['quality'] ?? 80;
                $suffix = $sizeConfig['suffix'] ?? $sizeName;

                // Nama file thumbnail
                $fileName = pathinfo($media->file_name, PATHINFO_FILENAME);
                $extension = $webpConversion ? 'webp' : pathinfo($media->file_name, PATHINFO_EXTENSION);
                $thumbName = $fileName . '_' . $suffix . '.' . $extension;

                // Path thumbnail
                $thumbRelative = $media->collection_name . '/' . $thumbnailDirectory . '/' . $thumbName;

                // Skip jika thumbnail sudah ada
                if (Storage::disk($disk)->exists($prefix . '/' . $thumbRelative)) {
                    continue;
                }

                // Pastikan direktori thumbnail ada
                $thumbDir = $prefix . '/' . $media->collection_name . '/' . $thumbnailDirectory;
                if (!Storage::disk($disk)->exists($thumbDir)) {
                    Storage::disk($disk)->makeDirectory($thumbDir);
                }

                // Generate thumbnail
                $thumbImage = clone $image;
                $thumbImage->scaleDown(width: $width, height: $height);

                if ($webpConversion) {
                    $encoded = $thumbImage->toWebp(quality: $quality);
                } else {
                    $encoded = $thumbImage->encode();
                }

                Storage::disk($disk)->put($prefix . '/' . $thumbRelative, (string) $encoded);
            }

            // Generate thumbnail default untuk backward compatibility
            $thumbName = pathinfo($media->file_name, PATHINFO_FILENAME) . '_thumb.webp';
            $relativeThumb = $media->collection_name . '/' . $thumbName;

            if (!Storage::disk($disk)->exists($prefix . '/' . $relativeThumb)) {
                $thumbImage = clone $image;
                $thumbImage->scaleDown(width: 150, height: 150);
                $encoded = $thumbImage->toWebp(quality: 80);
                Storage::disk($disk)->put($prefix . '/' . $relativeThumb, (string) $encoded);
            }

        } catch (\Throwable $e) {
            Log::error('Gagal generate thumbnail untuk media ID ' . $media->id . ': ' . $e->getMessage());
        }
    }
}
