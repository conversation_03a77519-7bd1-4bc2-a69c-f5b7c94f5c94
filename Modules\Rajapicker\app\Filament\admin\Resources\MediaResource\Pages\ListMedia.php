<?php

namespace Modules\Rajapicker\Filament\admin\Resources\MediaResource\Pages;

use Modules\Rajapicker\Models\RajaGaleri;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Modules\Rajapicker\Filament\admin\Resources\MediaResource;
use Modules\Rajapicker\Models\Media;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Intervention\Image\ImageManager;
use Modules\Rajapicker\Services\RajaPickerThumbnailService;
use Modules\Rajapicker\Services\RajaPickerConfigService;
// use Intervention\Image\Drivers\Gd\Driver as GdDriver; // tidak terpakai
// use Intervention\Image\Encoders\WebpEncoder; // tidak terpakai

class ListMedia extends ListRecords
{
    protected static string $resource = MediaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('upload')
                ->label('Upload Media')
                ->icon('heroicon-o-cloud-arrow-up')
                ->color('primary')
                ->form([
                    Grid::make()
                        ->schema([
                            Section::make('Upload File')
                                ->description('Pilih file yang ingin diupload ke media library')
                                ->schema([
                                    FileUpload::make('files')
                                        ->label('File')
                                        ->multiple()
                                        ->acceptedFileTypes([
                                            'image/jpeg',
                                            'image/png',
                                            'image/gif',
                                            'image/webp',
                                            'image/svg+xml',
                                            'application/pdf',
                                            'video/mp4',
                                            'video/avi',
                                            'video/mov',
                                            'audio/mp3',
                                            'audio/wav',
                                        ])
                                        ->maxSize(10240) // 10MB
                                        ->maxFiles(10)
                                        ->previewable()
                                        ->reorderable()
                                        ->required()
                                        ->helperText('Maksimal 10 file, ukuran per file maksimal 10MB')
                                        ->directory('media-uploads'),

                                    Select::make('collection_name')
                                        ->label('Koleksi')
                                        ->options($this->getAvailableCollections())
                                        ->default('default')
                                        ->required()
                                        ->helperText('Pilih koleksi untuk mengorganisir file'),
                                ]),
                        ])
                        ->columns(1),
                ])
                ->action(function (array $data) {
                    $this->handleSpatieMediaUpload($data);
                })
                ->modalWidth('2xl'),


            // Actions\CreateAction::make()
            //     ->label('Tambah Media Manual')
            //     ->icon('heroicon-o-document-plus'),

            // Action::make('cleanup')
            //     ->label('Bersihkan File Tidak Terpakai')
            //     ->icon('heroicon-o-trash')
            //     ->color('warning')
            //     ->requiresConfirmation()
            //     ->modalHeading('Bersihkan File Tidak Terpakai')
            //     ->modalDescription('Apakah Anda yakin ingin menghapus file media yang tidak terhubung dengan model apapun?')
            //     ->action(function () {
            //         $deletedCount = $this->cleanupUnusedMedia();
            //         Notification::make()
            //             ->title("Berhasil menghapus {$deletedCount} file yang tidak terpakai!")
            //             ->success()
            //             ->send();
            //     }),

            Action::make('generate_thumbnails')
                ->label('Generate Semua Thumbnail')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Generate Thumbnail')
                ->modalDescription('Generate thumbnail untuk semua gambar (gambar yang sudah memiliki thumbnail akan diabaikan). Lanjutkan?')
                ->action(function () {
                    $this->generateThumbnails();
                }),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('Semua Collection')
                ->badge(Media::where('mime_type', 'LIKE', 'image/%')->count()),
        ];

        // Dapatkan semua collection yang tersedia dari database
        $collections = Media::where('mime_type', 'LIKE', 'image/%')
            ->where('collection_name', '!=', 'conversion')
            ->whereNotLike('file_name', '%/conversion/%')
            ->distinct()
            ->pluck('collection_name')
            ->filter()
            ->sort();

        // Tambahkan tab untuk setiap collection
        foreach ($collections as $collection) {
            $count = Media::where('collection_name', $collection)
                ->where('mime_type', 'LIKE', 'image/%')
                ->count();

            $tabs[$collection] = Tab::make(ucfirst($collection))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('collection_name', $collection))
                ->badge($count);
        }

        return $tabs;
    }

    /**
     * Get available collections for upload form
     */
    protected function getAvailableCollections(): array
    {
        // Collection default yang selalu tersedia
        $defaultCollections = [
            'default' => 'Default',
            'gallery' => 'Gallery',
         
            'produk' => 'Products',
         
        ];

        // Dapatkan collection yang sudah ada di database
        $existingCollections = Media::distinct()
            ->pluck('collection_name')
            ->filter()
            ->mapWithKeys(function ($collection) {
                return [$collection => ucfirst($collection)];
            })
            ->toArray();

        // Gabungkan dan urutkan
        $allCollections = array_merge($defaultCollections, $existingCollections);
        ksort($allCollections);

        return $allCollections;
    }

    protected function cleanupUnusedMedia(): int
    {
        // Cari media yang tidak terhubung dengan model apapun (kecuali standalone)
        $unusedMedia = \Modules\Rajapicker\Models\Media::where(function ($query) {
            $query->whereDoesntHave('model')
                ->where('model_type', '!=', 'standalone');
        })->get();

        $count = $unusedMedia->count();

        foreach ($unusedMedia as $media) {
            $media->delete();
        }

        return $count;
    }

    public function getTitle(): string
    {
        return 'Media Library';
    }

    protected function handleSpatieMediaUpload(array $data)
    {
        try {
            // Initialize services
            $thumbnailService = new RajaPickerThumbnailService();
            $configService = new RajaPickerConfigService();

            $uploadedFiles = $data['files'] ?? [];
            $collectionName = $data['collection_name'] ?? 'default';
            $successCount = 0;
            $errorCount = 0;
            $thumbnailCount = 0;
            $errors = [];

            // Debug dengan dd untuk melihat data langsung
            if (empty($uploadedFiles)) {
                Notification::make()
                    ->title('Tidak ada file yang dipilih!')
                    ->body('Data yang diterima: ' . json_encode($data))
                    ->danger()
                    ->send();
                return;
            }

            foreach ($uploadedFiles as $uploadedFile) {
                try {
                    // Buat record RajaGaleri baru untuk setiap file
                    $mediaRecord = new RajaGaleri();
                    $mediaRecord->save();
                    // Simpan dulu untuk mendapatkan ID

                    // Coba menggunakan Storage facade untuk mendapatkan path yang benar
                    $tempPath = null;
                    $originalName = $uploadedFile;

                    $mlprefix = config('media-library.prefix', 'uploads');
                    // Coba berbagai disk dan path
                    $diskPaths = [
                        ['disk' => 'public', 'path' => $mlprefix . '/' . $uploadedFile],
                        ['disk' => 'public', 'path' => $uploadedFile],
                        ['disk' => 'local', 'path' => 'livewire-tmp/' . $uploadedFile],
                        ['disk' => 'local', 'path' => $uploadedFile],
                    ];

                    foreach ($diskPaths as $diskPath) {
                        if (Storage::disk($diskPath['disk'])->exists($diskPath['path'])) {
                            $tempPath = Storage::disk($diskPath['disk'])->path($diskPath['path']);
                            break;
                        }
                    }

                    $originalName = basename($uploadedFile);
                    $fileName = pathinfo($uploadedFile, PATHINFO_FILENAME);

                    // Tambahkan file ke media collection
                    $media = $mediaRecord
                        ->addMedia($tempPath)
                        ->usingName($fileName)
                        ->usingFileName($originalName)
                        ->toMediaCollection($collectionName);

                    if ($media) {
                        $successCount++;

                        $mediaRecord->update([
                            'record_id' => $media->id,
                            'nama' => $media->name,
                            'key' => $media->uuid,
                            'value' => $media->getUrl(),
                        ]);

                        // Generate thumbnail otomatis jika file adalah gambar
                        if ($this->isImageFile($media->mime_type)) {
                            $this->generateThumbnailsForMedia($media, $thumbnailService, $configService, $thumbnailCount);
                        }
                    } else {
                        $errorCount++;
                    }

                } catch (\Exception $e) {
                    $errors[] = "Error pada " . basename($uploadedFile) . ": " . $e->getMessage();
                    $errorCount++;
                }
            }

            // Kirim notifikasi berdasarkan hasil
            if ($successCount > 0 && $errorCount === 0) {
                $message = "Berhasil mengupload {$successCount} file!";
                if ($thumbnailCount > 0) {
                    $message .= " ({$thumbnailCount} thumbnail dibuat)";
                }
                Notification::make()
                    ->title($message)
                    ->success()
                    ->send();
            } elseif ($successCount > 0 && $errorCount > 0) {
                $message = "Berhasil: {$successCount} file, Gagal: {$errorCount} file";
                if ($thumbnailCount > 0) {
                    $message .= " ({$thumbnailCount} thumbnail dibuat)";
                }
                Notification::make()
                    ->title("Upload selesai dengan peringatan")
                    ->body($message)
                    ->warning()
                    ->send();
            } else {
                Notification::make()
                    ->title("Upload gagal!")
                    ->body("Semua file gagal diupload. " . implode(', ', array_slice($errors, 0, 3)))
                    ->danger()
                    ->send();
            }

        } catch (\Exception $e) {
            Log::error('Media upload general error: ' . $e->getMessage());

            Notification::make()
                ->title('Terjadi kesalahan saat upload!')
                ->body('Error: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \Modules\Rajapicker\Filament\admin\Resources\MediaResource\Widgets\MediaStatsWidget::class,
        ];
    }

    /**
     * Check if file is an image
     */
    protected function isImageFile(string $mimeType): bool
    {
        return str_starts_with($mimeType, 'image/');
    }

    /**
     * Generate thumbnails for uploaded media using RajaPicker configuration
     */
    protected function generateThumbnailsForMedia($media, RajaPickerThumbnailService $thumbnailService, RajaPickerConfigService $configService, int &$thumbnailCount): void
    {
        try {
            // Dapatkan path relatif file dari media library
            $prefix = config('media-library.prefix', 'uploads');
            $relativePath = $media->collection_name . '/' . $media->file_name;
            $fullPath = $prefix . '/' . $relativePath;

            // Generate thumbnail untuk semua ukuran yang diaktifkan
            $enabledSizes = $configService->getThumbnailSizes();

            foreach ($enabledSizes as $sizeName => $sizeConfig) {
                $thumbnailPath = $thumbnailService->generateThumbnail($fullPath, $sizeName);
                if ($thumbnailPath) {
                    $thumbnailCount++;
                    Log::info("Thumbnail berhasil dibuat untuk media ID {$media->id}: {$thumbnailPath}");
                }
            }

        } catch (\Exception $e) {
            Log::error("Error generating thumbnails for media ID {$media->id}: " . $e->getMessage());
        }
    }

    protected function generateThumbnails(): void
    {
        $prefix = config('media-library.prefix', 'uploads');
        $generated = 0;

        $manager = ImageManager::gd();

        Media::where('mime_type', 'like', 'image/%')->chunk(100, function ($medias) use (&$generated, $manager, $prefix) {
            foreach ($medias as $record) {
                $disk = $record->disk ?? 'public';

                $thumbName = pathinfo($record->file_name, PATHINFO_FILENAME) . '_thumb.webp';
                $relativeThumb = $record->collection_name . '/' . $thumbName;

                if (Storage::disk($disk)->exists($prefix . '/' . $relativeThumb)) {
                    continue; // sudah ada thumbnail
                }

                $originalRelative = $record->collection_name . '/' . $record->file_name;
                if (!Storage::disk($disk)->exists($prefix . '/' . $originalRelative)) {
                    continue; // file asli tidak ditemukan
                }

                $originalContent = Storage::disk($disk)->get($prefix . '/' . $originalRelative);

                try {
                    $encoded = $manager
                        ->read($originalContent)
                        ->scaleDown(300, 300)
                        ->toWebp(quality: 80);

                    Storage::disk($disk)->put($prefix . '/' . $relativeThumb, (string) $encoded);
                    $generated++;
                } catch (\Throwable $e) {
                    Log::error('Gagal generate thumbnail: ' . $e->getMessage());
                    continue;
                }
            }
        });

        Notification::make()
            ->title("Selesai! {$generated} thumbnail berhasil dibuat.")
            ->success()
            ->send();
    }
}
