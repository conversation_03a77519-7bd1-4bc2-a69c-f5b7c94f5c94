<?php

namespace Modules\Rajapicker\Filament\admin\Resources\MediaResource\Widgets;

use Modules\Rajapicker\Models\Media;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class MediaStatsWidget extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    protected function getColumns(): int
    {
        return 1;
    }

    protected function getStats(): array
    {
        $totalMedia = Media::count();
        $totalImages = Media::where('mime_type', 'like', 'image/%')->count();
        $totalVideos = Media::where('mime_type', 'like', 'video/%')->count();
        $totalDocuments = Media::where('mime_type', 'like', 'application/%')->count();

        // Hitung total ukuran file dalam MB
        $totalSize = Media::sum('size');
        $totalSizeMB = round($totalSize / 1024 / 1024, 2);

        return [
            Stat::make('Statistik Media Library', '')
                ->description("📁 Total Media: {$totalMedia} • 📷 Gambar: {$totalImages} • 🎬 Video: {$totalVideos} • 📄 Dokumen: {$totalDocuments} • 💾 Total Ukuran: {$totalSizeMB} MB")
                ->descriptionIcon('heroicon-m-photo')
                ->color('primary'),
        ];
    }
}
