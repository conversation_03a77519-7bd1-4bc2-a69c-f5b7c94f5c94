<?php

namespace Modules\Rajapicker\Filament\admin\Resources\MediaResource\Widgets;

use Modules\Rajapicker\Models\Media;
use Filament\Widgets\Widget;

class MediaStatsWidget extends Widget
{
    protected static string $view = 'rajapicker::filament.widgets.media-stats-card';

    protected function getViewData(): array
    {
        $totalMedia = Media::count();
        $totalImages = Media::where('mime_type', 'like', 'image/%')->count();
        $totalVideos = Media::where('mime_type', 'like', 'video/%')->count();
        $totalDocuments = Media::where('mime_type', 'like', 'application/%')->count();

        // Hitung total ukuran file dalam MB
        $totalSize = Media::sum('size');
        $totalSizeMB = round($totalSize / 1024 / 1024, 2);

        return [
            'totalMedia' => $totalMedia,
            'totalImages' => $totalImages,
            'totalVideos' => $totalVideos,
            'totalDocuments' => $totalDocuments,
            'totalSizeMB' => $totalSizeMB,
        ];
    }
}
