<?php

namespace Modules\Rajapicker\Filament\admin\Resources\MediaResource\Widgets;

use Modules\Rajapicker\Models\Media;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Support\Enums\IconPosition;

class MediaStatsWidget extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        $totalMedia = Media::count();
        $totalImages = Media::where('mime_type', 'like', 'image/%')->count();
        $totalVideos = Media::where('mime_type', 'like', 'video/%')->count();
        $totalDocuments = Media::where('mime_type', 'like', 'application/%')->count();

        // Hitung total ukuran file dalam MB
        $totalSize = Media::sum('size');
        $totalSizeMB = round($totalSize / 1024 / 1024, 2);

        return [
            Stat::make('Statistik Media Library', '')
                ->description("📁 Total Media: {$totalMedia} • 📷 Gambar: {$totalImages} • 🎬 Video: {$totalVideos} • 📄 Dokumen: {$totalDocuments} • 💾 Total Ukuran: {$totalSizeMB} MB")
                ->descriptionIcon('heroicon-m-photo')
                ->color('primary')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->extraAttributes([
                    'class' => 'col-span-full',
                ]),
        ];
    }
}
