# Media Upload Configuration

## Overview
FileUpload di Media Library sekarang mengikuti konfigurasi dari file config RajaPicker untuk konsistensi pengaturan upload di seluruh aplikasi.

## <PERSON>bahan yang Dilakukan

### 1. Konfigurasi Config RajaPicker
Config RajaPicker telah dipublish ke `config/rajapicker.php` dengan pengaturan:

```php
'defaults' => [
    'accepted_file_types' => [
        'image/jpeg',
        'image/png', 
        'image/gif',
        'image/webp'
    ],
    'max_file_size' => 10, // MB
    'collection' => 'default',
    // ... pengaturan lainnya
],

'collections' => [
    'default' => [...],
    'produk' => [...],
    'lainlain' => [...],
    'gallery' => [...],
],

'storage' => [
    'disk' => 'public',
    'path' => 'rajapicker',
    // ... pengaturan lainnya
],
```

### 2. Method createConfigurableFileUpload()
Dibuat method baru di `ListMedia.php` yang membaca konfigurasi dari config RajaPicker:

```php
protected function createConfigurableFileUpload(): FileUpload
{
    // Ambil konfigurasi dari config RajaPicker
    $config = config('rajapicker.defaults', []);
    $storageConfig = config('rajapicker.storage', []);
    
    // Gabungkan accepted file types dari config dengan file types tambahan untuk media library
    $acceptedTypes = array_merge(
        $config['accepted_file_types'] ?? ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        [
            'image/svg+xml',
            'application/pdf',
            'video/mp4',
            'video/avi', 
            'video/mov',
            'audio/mp3',
            'audio/wav',
        ]
    );
    
    // Ambil max file size dari config (dalam MB)
    $maxFileSize = $config['max_file_size'] ?? 10;
    $maxFileSizeKB = $maxFileSize * 1024; // Convert ke KB untuk Filament
    
    // Ambil directory dari storage config
    $directory = $storageConfig['path'] ?? 'media-uploads';
    
    // Ambil disk dari storage config
    $disk = $storageConfig['disk'] ?? 'public';
    
    return FileUpload::make('files')
        ->label('File')
        ->multiple()
        ->acceptedFileTypes($acceptedTypes)
        ->maxSize($maxFileSizeKB)
        ->maxFiles(10)
        ->previewable()
        ->reorderable()
        ->required()
        ->helperText("Maksimal 10 file, ukuran per file maksimal {$maxFileSize}MB. Tipe file yang didukung: " . implode(', ', array_map(fn($type) => str_replace(['image/', 'application/', 'video/', 'audio/'], '', $type), $acceptedTypes)))
        ->directory($directory)
        ->disk($disk);
}
```

### 3. Integrasi dengan Form Upload
Form upload sekarang menggunakan method `createConfigurableFileUpload()`:

```php
// Sebelum
FileUpload::make('files')
    ->label('File')
    ->multiple()
    ->acceptedFileTypes([...])
    ->maxSize(10240)
    // ... konfigurasi hardcoded

// Sesudah
$this->createConfigurableFileUpload(),
```

## Keunggulan Implementasi

### 1. **Konsistensi Konfigurasi**
- Semua pengaturan upload mengikuti satu sumber konfigurasi
- Tidak ada duplikasi pengaturan di berbagai tempat
- Mudah untuk mengubah pengaturan secara global

### 2. **Fleksibilitas**
- Mendukung file types dari config RajaPicker + file types tambahan untuk media library
- Collections dropdown otomatis mengikuti config collections
- Helper text dinamis berdasarkan konfigurasi

### 3. **Maintainability**
- Perubahan konfigurasi hanya perlu dilakukan di satu tempat
- Kode lebih bersih dan terorganisir
- Mudah untuk debugging dan troubleshooting

## Hasil Testing

✅ **Upload Modal**: Modal upload terbuka dengan benar
✅ **Helper Text**: Menampilkan informasi yang sesuai dengan config
✅ **File Types**: Mendukung semua file types dari config + tambahan untuk media library
✅ **Collections**: Dropdown collections menampilkan semua collections dari config
✅ **Max File Size**: Mengikuti pengaturan max_file_size dari config

## File yang Dimodifikasi

1. `config/rajapicker.php` - Config file yang dipublish
2. `Modules/Rajapicker/app/Filament/admin/Resources/MediaResource/Pages/ListMedia.php` - Method createConfigurableFileUpload()

## Cara Mengubah Konfigurasi

Untuk mengubah pengaturan upload, edit file `config/rajapicker.php`:

```php
// Mengubah ukuran maksimal file
'defaults' => [
    'max_file_size' => 15, // Ubah dari 10MB ke 15MB
],

// Menambah collection baru
'collections' => [
    'products' => [
        'name' => 'Product Images',
        'description' => 'Collection untuk gambar produk',
        'accepted_file_types' => ['image/jpeg', 'image/png', 'image/webp'],
        'max_file_size' => 5,
        'directory' => 'products',
    ],
],
```

Perubahan akan langsung berlaku tanpa perlu restart server.
