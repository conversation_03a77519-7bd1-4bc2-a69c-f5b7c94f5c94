<x-filament-widgets::widget>
    <x-filament::section>
        <div class="grid grid-cols-1 gap-6">
            <!-- Header Card -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Statistik Media Library</h3>
                    <div class="flex items-center space-x-2">
                        <x-heroicon-m-photo class="w-5 h-5 text-gray-500" />
                    </div>
                </div>
                
                <!-- Stats Grid -->
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                    <!-- Total Media -->
                    <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-100">
                        <div class="flex items-center justify-center mb-2">
                            <x-heroicon-m-photo class="w-6 h-6 text-blue-600" />
                        </div>
                        <div class="text-2xl font-bold text-blue-600">{{ $totalMedia }}</div>
                        <div class="text-sm text-blue-700 font-medium">Total Media</div>
                        <div class="text-xs text-blue-600 mt-1">File media di library 📁</div>
                    </div>
                    
                    <!-- Gambar -->
                    <div class="text-center p-4 bg-green-50 rounded-lg border border-green-100">
                        <div class="flex items-center justify-center mb-2">
                            <x-heroicon-m-camera class="w-6 h-6 text-green-600" />
                        </div>
                        <div class="text-2xl font-bold text-green-600">{{ $totalImages }}</div>
                        <div class="text-sm text-green-700 font-medium">Gambar</div>
                        <div class="text-xs text-green-600 mt-1">File gambar 📷</div>
                    </div>
                    
                    <!-- Video -->
                    <div class="text-center p-4 bg-cyan-50 rounded-lg border border-cyan-100">
                        <div class="flex items-center justify-center mb-2">
                            <x-heroicon-m-video-camera class="w-6 h-6 text-cyan-600" />
                        </div>
                        <div class="text-2xl font-bold text-cyan-600">{{ $totalVideos }}</div>
                        <div class="text-sm text-cyan-700 font-medium">Video</div>
                        <div class="text-xs text-cyan-600 mt-1">File video 🎬</div>
                    </div>
                    
                    <!-- Dokumen -->
                    <div class="text-center p-4 bg-orange-50 rounded-lg border border-orange-100">
                        <div class="flex items-center justify-center mb-2">
                            <x-heroicon-m-document class="w-6 h-6 text-orange-600" />
                        </div>
                        <div class="text-2xl font-bold text-orange-600">{{ $totalDocuments }}</div>
                        <div class="text-sm text-orange-700 font-medium">Dokumen</div>
                        <div class="text-xs text-orange-600 mt-1">File dokumen 📄</div>
                    </div>
                    
                    <!-- Total Ukuran -->
                    <div class="text-center p-4 bg-gray-50 rounded-lg border border-gray-100">
                        <div class="flex items-center justify-center mb-2">
                            <x-heroicon-m-server class="w-6 h-6 text-gray-600" />
                        </div>
                        <div class="text-2xl font-bold text-gray-600">{{ $totalSizeMB }} MB</div>
                        <div class="text-sm text-gray-700 font-medium">Total Ukuran</div>
                        <div class="text-xs text-gray-600 mt-1">Ruang storage terpakai 💾</div>
                    </div>
                </div>
                
                <!-- Summary Bar -->
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between text-sm text-gray-600">
                        <span>Media Library Overview</span>
                        <span class="font-medium">{{ $totalMedia }} total files • {{ $totalSizeMB }} MB used</span>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
