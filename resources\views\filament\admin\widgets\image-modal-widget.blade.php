<!-- Image Detail Modal -->
<div id="imageModal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-75 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b">
            <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">Detail Gambar</h3>
            <button onclick="closeImageModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="overflow-y-auto max-h-[calc(90vh-140px)]">
            <!-- Loading State -->
            <div id="modalLoading" class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-2 text-gray-600">Memuat detail gambar...</p>
            </div>

            <!-- Error State -->
            <div id="modalError" class="hidden text-center py-8">
                <div class="text-red-600 mb-2">
                    <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <p class="text-gray-600">Gagal memuat detail gambar</p>
            </div>

            <!-- Content Container - Split Layout -->
            <div id="modalContent" class="hidden">
                <div class="flex flex-col lg:flex-row min-h-[500px]">
                    <!-- Left Side - Information -->
                    <div class="lg:w-1/2 p-6 border-r border-gray-200 overflow-y-auto">
                        <!-- Basic Information -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Informasi Dasar</h4>
                            <div class="space-y-3">
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">Nama File:</span>
                                    <span id="modalFileName" class="text-gray-900 break-all text-sm sm:text-base"></span>
                                </div>
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">ID:</span>
                                    <span id="modalId" class="text-gray-900"></span>
                                </div>
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">Koleksi:</span>
                                    <span id="modalCollection" class="text-gray-900 capitalize"></span>
                                </div>
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">Ukuran File:</span>
                                    <span id="modalSize" class="text-gray-900"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Information -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Informasi Teknis</h4>
                            <div class="space-y-3">
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">Dimensi:</span>
                                    <span id="modalDimensions" class="text-gray-900"></span>
                                </div>
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">Tipe MIME:</span>
                                    <span id="modalMimeType" class="text-gray-900"></span>
                                </div>
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">Dibuat:</span>
                                    <span id="modalCreatedAt" class="text-gray-900"></span>
                                </div>
                                <div class="flex flex-col sm:flex-row sm:justify-between">
                                    <span class="font-medium text-gray-700">Diupdate:</span>
                                    <span id="modalUpdatedAt" class="text-gray-900"></span>
                                </div>
                            </div>
                        </div>

                        <!-- URL Section -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">URL</h4>
                            <div class="space-y-4">
                                <!-- Original URL -->
                                <div id="originalUrlSection">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">URL Asli:</label>
                                    <div class="flex">
                                        <input type="text" id="modalOriginalUrl" readonly
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm">
                                        <button onclick="copyToClipboard('modalOriginalUrl')"
                                                class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors text-sm">
                                            Copy
                                        </button>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>

                    <!-- Right Side - Image -->
                    <div class="lg:w-1/2 p-6 flex items-center justify-center bg-gray-50">
                        <div class="text-center">
                            <img id="modalImage" src="" alt="" class="max-w-full h-auto rounded-lg " style="max-width: 800px; max-height: 500px;">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
            <button onclick="closeImageModal()" 
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                Tutup
            </button>
        </div>
    </div>
</div>

<script>
// Global functions for image modal
window.openImageModal = function(mediaId) {
    const modal = document.getElementById('imageModal');
    const loading = document.getElementById('modalLoading');
    const content = document.getElementById('modalContent');
    const error = document.getElementById('modalError');
    
    // Show modal and loading state
    modal.classList.remove('hidden');
    loading.classList.remove('hidden');
    content.classList.add('hidden');
    error.classList.add('hidden');
    document.body.style.overflow = 'hidden';
    
    // Fetch media details from API
    fetch(`/api/media/${mediaId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Hide loading, show content
        loading.classList.add('hidden');
        content.classList.remove('hidden');
        
        // Populate modal with data
        populateModal(data);
    })
    .catch(error => {
        console.error('Error fetching media details:', error);
        loading.classList.add('hidden');
        document.getElementById('modalError').classList.remove('hidden');
    });
};

window.closeImageModal = function() {
    const modal = document.getElementById('imageModal');
    modal.classList.add('hidden');
    document.body.style.overflow = '';
};

window.copyToClipboard = function(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = element.nextElementSibling;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('bg-green-600');
    button.classList.remove('bg-blue-600');
    
    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('bg-green-600');
        button.classList.add('bg-blue-600');
    }, 2000);
};

function populateModal(data) {
    // Set image
    const modalImage = document.getElementById('modalImage');
    modalImage.src = data.original_url;
    modalImage.alt = data.name || data.file_name;
    
    // Set basic info
    document.getElementById('modalFileName').textContent = data.name || data.file_name;
    document.getElementById('modalId').textContent = data.id;
    document.getElementById('modalCollection').textContent = data.collection_name;
    document.getElementById('modalSize').textContent = data.size_human;
    
    // Set technical info
    const dimensions = data.dimensions ? `${data.dimensions.width} × ${data.dimensions.height} px` : 'N/A';
    document.getElementById('modalDimensions').textContent = dimensions;
    document.getElementById('modalMimeType').textContent = data.mime_type;
    
    // Format dates
    const createdAt = new Date(data.created_at).toLocaleString('id-ID');
    const updatedAt = new Date(data.updated_at).toLocaleString('id-ID');
    document.getElementById('modalCreatedAt').textContent = createdAt;
    document.getElementById('modalUpdatedAt').textContent = updatedAt;
    
    // Set URLs with app_url
    const appUrl = '{{ config("app.url") }}';
    const fullUrl = data.original_url.startsWith('http') ? data.original_url : appUrl + data.original_url;
    document.getElementById('modalOriginalUrl').value = fullUrl;
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('imageModal');
    if (event.target === modal) {
        closeImageModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('imageModal');
        if (!modal.classList.contains('hidden')) {
            closeImageModal();
        }
    }
});
</script>
