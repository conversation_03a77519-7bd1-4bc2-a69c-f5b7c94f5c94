<!-- Image Detail Modal -->
<div id="imageModal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-75 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b">
            <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">Detail Gambar</h3>
            <button onclick="closeImageModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- Loading State -->
            <div id="modalLoading" class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-2 text-gray-600">Memuat detail gambar...</p>
            </div>

            <!-- Content Container -->
            <div id="modalContent" class="hidden">
                <!-- Image Container -->
                <div class="mb-6 text-center">
                    <img id="modalImage" src="" alt="" class="max-w-full h-auto rounded-lg shadow-lg mx-auto" style="max-width: 800px;">
                </div>

                <!-- Image Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Info -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-gray-900 border-b pb-2">Informasi Dasar</h4>
                        
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Nama File:</span>
                                <span id="modalFileName" class="font-medium text-gray-900"></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">ID:</span>
                                <span id="modalId" class="font-medium text-gray-900"></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Koleksi:</span>
                                <span id="modalCollection" class="font-medium text-gray-900 capitalize"></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Ukuran File:</span>
                                <span id="modalSize" class="font-medium text-gray-900"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Info -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-gray-900 border-b pb-2">Informasi Teknis</h4>
                        
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Dimensi:</span>
                                <span id="modalDimensions" class="font-medium text-gray-900"></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tipe MIME:</span>
                                <span id="modalMimeType" class="font-medium text-gray-900"></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Dibuat:</span>
                                <span id="modalCreatedAt" class="font-medium text-gray-900"></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Diupdate:</span>
                                <span id="modalUpdatedAt" class="font-medium text-gray-900"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- URL Section -->
                <div class="mt-6 space-y-4">
                    <h4 class="text-md font-semibold text-gray-900 border-b pb-2">URL</h4>
                    
                    <div class="space-y-2">
                        <div>
                            <label class="text-sm text-gray-600">URL Asli:</label>
                            <div class="flex items-center space-x-2 mt-1">
                                <input id="modalOriginalUrl" type="text" readonly 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm">
                                <button onclick="copyToClipboard('modalOriginalUrl')" 
                                        class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                        
                        <div id="thumbnailUrlSection" class="hidden">
                            <label class="text-sm text-gray-600">URL Thumbnail:</label>
                            <div class="flex items-center space-x-2 mt-1">
                                <input id="modalThumbnailUrl" type="text" readonly 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm">
                                <button onclick="copyToClipboard('modalThumbnailUrl')" 
                                        class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error State -->
            <div id="modalError" class="hidden text-center py-8">
                <div class="text-red-600 mb-2">
                    <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <p class="text-gray-600">Gagal memuat detail gambar</p>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
            <button onclick="closeImageModal()" 
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                Tutup
            </button>
        </div>
    </div>
</div>

<script>
// Global functions for image modal
window.openImageModal = function(mediaId) {
    const modal = document.getElementById('imageModal');
    const loading = document.getElementById('modalLoading');
    const content = document.getElementById('modalContent');
    const error = document.getElementById('modalError');
    
    // Show modal and loading state
    modal.classList.remove('hidden');
    loading.classList.remove('hidden');
    content.classList.add('hidden');
    error.classList.add('hidden');
    document.body.style.overflow = 'hidden';
    
    // Fetch media details from API
    fetch(`/api/media/${mediaId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Hide loading, show content
        loading.classList.add('hidden');
        content.classList.remove('hidden');
        
        // Populate modal with data
        populateModal(data);
    })
    .catch(error => {
        console.error('Error fetching media details:', error);
        loading.classList.add('hidden');
        document.getElementById('modalError').classList.remove('hidden');
    });
};

window.closeImageModal = function() {
    const modal = document.getElementById('imageModal');
    modal.classList.add('hidden');
    document.body.style.overflow = '';
};

window.copyToClipboard = function(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = element.nextElementSibling;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('bg-green-600');
    button.classList.remove('bg-blue-600');
    
    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('bg-green-600');
        button.classList.add('bg-blue-600');
    }, 2000);
};

function populateModal(data) {
    // Set image
    const modalImage = document.getElementById('modalImage');
    modalImage.src = data.original_url;
    modalImage.alt = data.name || data.file_name;
    
    // Set basic info
    document.getElementById('modalFileName').textContent = data.name || data.file_name;
    document.getElementById('modalId').textContent = data.id;
    document.getElementById('modalCollection').textContent = data.collection_name;
    document.getElementById('modalSize').textContent = data.size_human;
    
    // Set technical info
    const dimensions = data.dimensions ? `${data.dimensions.width} × ${data.dimensions.height} px` : 'N/A';
    document.getElementById('modalDimensions').textContent = dimensions;
    document.getElementById('modalMimeType').textContent = data.mime_type;
    
    // Format dates
    const createdAt = new Date(data.created_at).toLocaleString('id-ID');
    const updatedAt = new Date(data.updated_at).toLocaleString('id-ID');
    document.getElementById('modalCreatedAt').textContent = createdAt;
    document.getElementById('modalUpdatedAt').textContent = updatedAt;
    
    // Set URLs
    document.getElementById('modalOriginalUrl').value = data.original_url;
    
    // Show thumbnail URL if available
    const thumbnailSection = document.getElementById('thumbnailUrlSection');
    if (data.thumbnail_url) {
        document.getElementById('modalThumbnailUrl').value = data.thumbnail_url;
        thumbnailSection.classList.remove('hidden');
    } else {
        thumbnailSection.classList.add('hidden');
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('imageModal');
    if (event.target === modal) {
        closeImageModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('imageModal');
        if (!modal.classList.contains('hidden')) {
            closeImageModal();
        }
    }
});
</script>
